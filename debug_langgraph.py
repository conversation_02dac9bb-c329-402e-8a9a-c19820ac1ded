#!/usr/bin/env python3
"""
调试LangGraph流式处理
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.app.iot.service.langgraph_agent_service import LangGraphAgentService
from loguru import logger

async def debug_langgraph():
    """调试LangGraph流式处理"""
    print("🔍 开始调试LangGraph流式处理")
    
    try:
        # 创建服务实例
        service = LangGraphAgentService()
        
        # 初始化服务
        print("📋 初始化LangGraph服务...")
        await service.initialize()
        print("✅ LangGraph服务初始化成功")
        
        # 测试流式对话
        print("\n🧪 测试流式对话...")
        session_id = "debug_session_001"
        user_id = 1
        message = "计算3+2"
        
        print(f"📝 消息: {message}")
        print(f"📋 Session ID: {session_id}")
        print("=" * 60)
        
        event_count = 0
        async for event in service.stream_conversation(
            message=message,
            session_id=session_id,
            user_id=user_id
        ):
            event_count += 1
            event_type = event.get("type", "unknown")
            
            print(f"[{event_count:03d}] 事件类型: {event_type}")
            
            if event_type == "delta":
                content = event.get("content", "")
                content_type = event.get("content_type", "unknown")
                node = event.get("node", "unknown")
                print(f"      内容类型: {content_type}")
                print(f"      节点: {node}")
                print(f"      内容: {repr(content)}")
            
            elif event_type == "node_start":
                node = event.get("node", "unknown")
                print(f"      节点开始: {node}")
            
            elif event_type == "node_complete":
                node = event.get("node", "unknown")
                print(f"      节点完成: {node}")
            
            elif event_type == "tool_call":
                tool_name = event.get("tool_name", "unknown")
                node = event.get("node", "unknown")
                print(f"      工具调用: {tool_name} (节点: {node})")
            
            elif event_type == "completion":
                print(f"      会话完成")
                break
            
            elif event_type == "error":
                error = event.get("error", "unknown")
                print(f"      错误: {error}")
                break
            
            else:
                print(f"      数据: {event}")
            
            print("-" * 40)
        
        print(f"\n📊 总事件数: {event_count}")
        
    except Exception as e:
        logger.error(f"调试失败: {e}")
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_langgraph())
