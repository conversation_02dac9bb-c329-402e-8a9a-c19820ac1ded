#!/usr/bin/env python3
"""
测试服务层使用纯messages模式
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.app.iot.service.langgraph_agent_service import LangGraphAgentService
from langchain_core.messages import HumanMessage
from loguru import logger

async def debug_service_messages_only():
    """测试服务层使用纯messages模式"""
    print("🔍 测试服务层使用纯messages模式")
    
    try:
        # 创建服务实例
        service = LangGraphAgentService()
        
        # 初始化服务
        print("📋 初始化LangGraph服务...")
        await service.initialize()
        print("✅ LangGraph服务初始化成功")
        
        # 创建初始状态
        initial_state = {
            "messages": [HumanMessage(content="计算3+2")],
            "session_id": "debug_service_001",
            "user_id": 1,
            "tool_results": [],
            "execution_metadata": {
                "start_time": "2025-09-10T17:00:00",
                "execution_id": "test-service-001"
            }
        }
        
        # 配置运行参数
        run_config = {
            "configurable": {
                "thread_id": "debug_service_001",
                "user_id": 1
            }
        }
        
        print("\n🧪 测试服务层纯messages模式...")
        print("=" * 60)
        
        event_count = 0
        
        # 重置流式处理状态
        service.streaming_core.reset_state()
        
        # 直接使用graph.astream with messages模式
        async for stream_mode, chunk in service.graph.astream(
            initial_state,
            stream_mode=["messages"],  # 只使用messages模式
            config=run_config
        ):
            event_count += 1
            
            if stream_mode == "messages":
                if isinstance(chunk, tuple) and len(chunk) == 2:
                    message_chunk, metadata = chunk
                    node_name = metadata.get("langgraph_node", "unknown")
                    
                    # 检测节点切换
                    if service.streaming_core.current_node != node_name:
                        service.streaming_core.current_node = node_name
                        service.streaming_core.node_accumulated_content[node_name] = ""
                        print(f"[{event_count:03d}] 节点切换: {node_name}")
                        print("-" * 40)
                        event_count += 1
                    
                    if hasattr(message_chunk, "content") and message_chunk.content is not None:
                        token_content = message_chunk.content
                        accumulated = service.streaming_core.node_accumulated_content.get(node_name, "")
                        
                        # 使用统一的token处理逻辑
                        stream_event = await service.streaming_core.process_token_stream(
                            token_content, node_name, accumulated
                        )
                        
                        if stream_event:
                            print(f"[{event_count:03d}] Node: {node_name}")
                            print(f"      Type: {stream_event.type}")
                            print(f"      Token: {repr(stream_event.content)}")
                            print(f"      Length: {len(stream_event.content)}")
                            print("-" * 40)
        
        print(f"\n📊 总事件数: {event_count}")
        
    except Exception as e:
        logger.error(f"调试失败: {e}")
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_service_messages_only())
