#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI聊天服务层

提供AI聊天功能的业务逻辑实现，支持LangChain工具调用
"""
import uuid
import time
import json
from typing import List, Optional, AsyncGenerator, Dict, Any, Union
import httpx
from loguru import logger
from datetime import datetime

# LangChain imports
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
from langchain_core.tools import BaseTool as LangChainBaseTool
from langchain_openai import ChatOpenAI
from langchain_core.language_models import BaseChatModel

from backend.app.iot.schema.ai_chat import (
    ChatMessage,
    ChatResponse,
    ChatStreamResponse,
    VLLMChatRequest,
    VLLMChatResponse,
    AIModelInfo,
    HealthStatus
)
from backend.core.conf import settings
from backend.core.ai_config import (
    get_ai_config,
    get_model_config,
    get_available_models,
    validate_model_parameters,
    get_system_prompt,
    format_chat_history_for_api
)
from backend.app.iot.service.streaming_core_service import StreamingCoreService, StreamingServiceMixin, StreamEvent


class AIChatService(StreamingServiceMixin):
    """AI聊天服务类"""

    def __init__(self):
        super().__init__()  # 初始化StreamingServiceMixin
        # 获取AI服务配置
        self.config = get_ai_config()

        # 内存中的会话存储（生产环境应使用数据库）
        self.chat_sessions: Dict[str, List[ChatMessage]] = {}
    
    async def chat(
        self,
        message: str,
        user_id: int,
        session_id: Optional[str] = None,
        model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = 0.7
    ) -> ChatResponse:
        """
        AI聊天对话
        
        Args:
            message: 用户消息
            user_id: 用户ID
            session_id: 会话ID
            model: 使用的模型
            max_tokens: 最大token数量
            temperature: 生成温度
            
        Returns:
            ChatResponse: 聊天响应
        """
        start_time = time.time()
        
        # 生成会话ID
        if not session_id:
            session_id = str(uuid.uuid4())
        
        # 获取聊天历史
        chat_history = self._get_session_history(session_id)
        
        # 添加用户消息到历史
        user_message = ChatMessage(
            id=str(uuid.uuid4()),
            role="user",
            content=message,
            session_id=session_id,
            user_id=user_id
        )
        chat_history.append(user_message)
        
        try:
            # 验证和标准化参数
            validated_params = validate_model_parameters(
                model_name=model or self.config.default_model,
                max_tokens=max_tokens,
                temperature=temperature
            )

            # 构建vLLM API请求
            vllm_request = self._build_vllm_request(
                chat_history=chat_history,
                **validated_params
            )

            # 调用远程AI服务
            async with httpx.AsyncClient(timeout=self.config.timeout) as client:
                response = await client.post(
                    f"{self.config.base_url}/chat/completions",
                    json=vllm_request.dict(),
                    headers={"Content-Type": "application/json"}
                )
                
                if response.status_code != 200:
                    raise Exception(f"AI服务调用失败: {response.status_code} {response.text}")
                
                vllm_response = VLLMChatResponse(**response.json())
            
            # 提取AI回复
            ai_reply = vllm_response.choices[0]["message"]["content"]
            
            # 添加AI回复到历史
            ai_message = ChatMessage(
                id=str(uuid.uuid4()),
                role="assistant",
                content=ai_reply,
                session_id=session_id,
                user_id=user_id
            )
            chat_history.append(ai_message)
            
            # 更新会话历史
            self.chat_sessions[session_id] = chat_history
            
            # 计算响应时间
            response_time = time.time() - start_time
            
            return ChatResponse(
                id=str(uuid.uuid4()),
                reply=ai_reply,
                session_id=session_id,
                model_used=vllm_response.model,
                tokens_used=vllm_response.usage,
                response_time=response_time
            )
            
        except Exception as e:
            logger.error(f"AI聊天失败: {str(e)}")
            raise Exception(f"AI聊天失败: {str(e)}")
    
    async def chat_stream(
        self,
        message: str,
        user_id: int,
        session_id: Optional[str] = None,
        model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = 0.7
    ) -> AsyncGenerator[ChatStreamResponse, None]:
        """
        AI流式聊天对话
        
        Args:
            message: 用户消息
            user_id: 用户ID
            session_id: 会话ID
            model: 使用的模型
            max_tokens: 最大token数量
            temperature: 生成温度
            
        Yields:
            ChatStreamResponse: 流式响应
        """
        # 生成会话ID
        if not session_id:
            session_id = str(uuid.uuid4())
        
        # 获取聊天历史
        chat_history = self._get_session_history(session_id)
        
        # 添加用户消息到历史
        user_message = ChatMessage(
            id=str(uuid.uuid4()),
            role="user",
            content=message,
            session_id=session_id,
            user_id=user_id
        )
        chat_history.append(user_message)
        
        try:
            # 验证和标准化参数
            validated_params = validate_model_parameters(
                model_name=model or self.config.default_model,
                max_tokens=max_tokens,
                temperature=temperature
            )

            # 构建vLLM API请求（流式）
            vllm_request = self._build_vllm_request(
                chat_history=chat_history,
                stream=True,
                **validated_params
            )
            
            response_id = str(uuid.uuid4())
            full_response = ""
            
            # 调用远程AI服务（流式）
            logger.info(f"开始调用AI服务流式接口: {self.config.base_url}/chat/completions")
            logger.debug(f"请求参数: {vllm_request.dict()}")

            async with httpx.AsyncClient(timeout=self.config.stream_timeout) as client:
                async with client.stream(
                    "POST",
                    f"{self.config.base_url}/chat/completions",
                    json=vllm_request.dict(),
                    headers={"Content-Type": "application/json"}
                ) as response:

                    logger.info(f"AI服务响应状态码: {response.status_code}")

                    if response.status_code != 200:
                        error_text = await response.aread()
                        logger.error(f"AI服务调用失败: {response.status_code}, 响应: {error_text}")
                        raise Exception(f"AI服务调用失败: {response.status_code}, 详情: {error_text}")
                    
                    logger.info("开始处理流式响应数据")
                    chunk_count = 0

                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            data = line[6:]  # 移除 "data: " 前缀

                            if data == "[DONE]":
                                logger.info(f"流式响应完成，共处理 {chunk_count} 个数据块，总长度: {len(full_response)}")
                                break
                            
                            try:
                                chunk_data = json.loads(data)
                                chunk_count += 1

                                if "choices" in chunk_data and chunk_data["choices"]:
                                    delta = chunk_data["choices"][0].get("delta", {})
                                    content = delta.get("content", "")

                                    if content:
                                        full_response += content

                                        yield ChatStreamResponse(
                                            id=response_id,
                                            delta=content,
                                            session_id=session_id,
                                            model_used=chunk_data.get("model", model or self.config.default_model),
                                            finish_reason=chunk_data["choices"][0].get("finish_reason")
                                        )

                                    # 检查是否有finish_reason
                                    finish_reason = chunk_data["choices"][0].get("finish_reason")
                                    if finish_reason:
                                        logger.info(f"流式响应结束，原因: {finish_reason}")

                                else:
                                    logger.warning(f"流式响应数据格式异常: {chunk_data}")

                            except json.JSONDecodeError as e:
                                logger.warning(f"解析流式响应JSON失败: {e}, 原始数据: {data[:100]}...")
                                continue
                            except Exception as e:
                                logger.error(f"处理流式响应数据异常: {e}")
                                continue
            
            # 添加完整的AI回复到历史
            if full_response:
                ai_message = ChatMessage(
                    id=str(uuid.uuid4()),
                    role="assistant",
                    content=full_response,
                    session_id=session_id,
                    user_id=user_id
                )
                chat_history.append(ai_message)
                self.chat_sessions[session_id] = chat_history
                
        except Exception as e:
            logger.error(f"AI流式聊天失败: {str(e)}")
            logger.error(f"请求参数: {vllm_request.dict() if 'vllm_request' in locals() else 'N/A'}")
            logger.error(f"会话ID: {session_id}, 用户ID: {user_id}")
            raise Exception(f"AI流式聊天失败: {str(e)}")
    
    def _get_session_history(self, session_id: str) -> List[ChatMessage]:
        """获取会话历史"""
        return self.chat_sessions.get(session_id, [])
    
    def _build_vllm_request(
        self,
        chat_history: List[ChatMessage],
        model: str,
        max_tokens: int,
        temperature: float,
        top_p: float = None,
        stream: bool = False
    ) -> VLLMChatRequest:
        """构建vLLM API请求"""

        # 转换消息格式
        messages = []

        # 添加系统提示词
        messages.append({
            "role": "system",
            "content": get_system_prompt()
        })

        # 格式化聊天历史
        api_messages = format_chat_history_for_api(chat_history)
        messages.extend(api_messages)

        # 智能调整max_tokens，确保不超过模型上下文限制
        model_context_limit = 40960  # Qwen3-32B-AWQ的上下文限制

        # 估算输入消息的token数量（粗略估算：1个字符约等于1个token）
        input_tokens = sum(len(msg["content"]) for msg in messages)

        # 确保总token数不超过模型限制，留出一些缓冲
        max_available_tokens = model_context_limit - input_tokens - 100  # 留100个token缓冲
        adjusted_max_tokens = min(max_tokens, max_available_tokens)

        if adjusted_max_tokens <= 0:
            raise Exception("输入消息过长，超出模型上下文限制")

        if adjusted_max_tokens < max_tokens:
            logger.warning(f"调整max_tokens从 {max_tokens} 到 {adjusted_max_tokens}，以适应模型上下文限制")

        return VLLMChatRequest(
            model=model,
            messages=messages,
            max_tokens=adjusted_max_tokens,
            temperature=temperature,
            top_p=top_p or self.config.default_top_p,
            stream=stream
        )

    async def get_chat_history(
        self,
        session_id: str,
        user_id: int
    ) -> List[ChatMessage]:
        """
        获取聊天历史记录

        Args:
            session_id: 会话ID
            user_id: 用户ID

        Returns:
            List[ChatMessage]: 聊天历史记录
        """
        try:
            history = self.chat_sessions.get(session_id, [])
            # 过滤用户的消息
            user_history = [msg for msg in history if msg.user_id == user_id]
            return user_history
        except Exception as e:
            logger.error(f"获取聊天历史失败: {str(e)}")
            raise Exception(f"获取聊天历史失败: {str(e)}")

    async def clear_chat_history(
        self,
        session_id: str,
        user_id: int
    ) -> None:
        """
        清空聊天历史记录

        Args:
            session_id: 会话ID
            user_id: 用户ID
        """
        try:
            if session_id in self.chat_sessions:
                # 只清空该用户的消息
                history = self.chat_sessions[session_id]
                filtered_history = [msg for msg in history if msg.user_id != user_id]

                if filtered_history:
                    self.chat_sessions[session_id] = filtered_history
                else:
                    del self.chat_sessions[session_id]

            logger.info(f"已清空用户 {user_id} 的会话 {session_id} 历史记录")
        except Exception as e:
            logger.error(f"清空聊天历史失败: {str(e)}")
            raise Exception(f"清空聊天历史失败: {str(e)}")

    async def get_available_models(self) -> List[AIModelInfo]:
        """
        获取可用的AI模型列表

        Returns:
            List[AIModelInfo]: 可用模型列表
        """
        try:
            # 从配置获取预定义的模型信息
            models_config = get_available_models()
            models = []

            for model_name, model_config in models_config.items():
                models.append(AIModelInfo(
                    name=model_config.name,
                    display_name=model_config.display_name,
                    description=model_config.description,
                    max_tokens=model_config.max_tokens,
                    supports_stream=model_config.supports_stream,
                    status=model_config.status
                ))

            # 可以通过调用远程服务验证模型可用性
            try:
                async with httpx.AsyncClient(timeout=self.config.health_check_timeout) as client:
                    response = await client.get(f"{self.config.base_url}/models")
                    if response.status_code == 200:
                        # 解析远程服务返回的模型列表
                        remote_models = response.json()
                        logger.info(f"从远程服务获取到模型列表: {remote_models}")

                        # 更新模型状态
                        if "data" in remote_models:
                            available_model_names = {model["id"] for model in remote_models["data"]}
                            for model in models:
                                if model.name in available_model_names:
                                    model.status = "available"
                                else:
                                    model.status = "unavailable"
            except Exception as e:
                logger.warning(f"无法从远程服务获取模型列表: {str(e)}")
                # 如果无法连接远程服务，将所有模型标记为不可用
                for model in models:
                    model.status = "unknown"

            return models
        except Exception as e:
            logger.error(f"获取模型列表失败: {str(e)}")
            raise Exception(f"获取模型列表失败: {str(e)}")

    async def health_check(self) -> HealthStatus:
        """
        AI服务健康检查

        Returns:
            HealthStatus: 健康状态信息
        """
        start_time = time.time()

        try:
            # 检查远程AI服务状态
            async with httpx.AsyncClient(timeout=self.config.health_check_timeout) as client:
                response = await client.get(f"{self.config.base_url}/models")

                if response.status_code == 200:
                    ai_service_status = "healthy"
                    available_models = [self.config.default_model]

                    # 尝试解析模型列表
                    try:
                        models_data = response.json()
                        if "data" in models_data:
                            available_models = [model["id"] for model in models_data["data"]]
                    except:
                        pass
                else:
                    ai_service_status = "unhealthy"
                    available_models = []

            response_time = time.time() - start_time

            return HealthStatus(
                status="healthy" if ai_service_status == "healthy" else "degraded",
                ai_service_url=self.config.base_url,
                ai_service_status=ai_service_status,
                response_time=response_time,
                available_models=available_models
            )

        except Exception as e:
            response_time = time.time() - start_time
            logger.error(f"AI服务健康检查失败: {str(e)}")

            return HealthStatus(
                status="unhealthy",
                ai_service_url=self.config.base_url,
                ai_service_status="unreachable",
                response_time=response_time,
                available_models=[]
            )

    def _get_langchain_model(self, model_name: str) -> BaseChatModel:
        """
        获取LangChain兼容的模型实例

        Args:
            model_name: 模型名称

        Returns:
            BaseChatModel: LangChain模型实例
        """
        model_config = get_model_config(model_name)

        # 根据模型配置创建相应的LangChain模型
        if "openai" in model_name.lower() or "gpt" in model_name.lower():
            return ChatOpenAI(
                model=model_name,
                temperature=0.7,
                max_tokens=model_config.max_tokens,
                openai_api_base=self.config.base_url,
                openai_api_key=self.config.api_key or "dummy-key",
                streaming=True
            )
        else:
            # 对于其他模型，使用通用的ChatOpenAI接口
            return ChatOpenAI(
                model=model_name,
                temperature=0.7,
                max_tokens=min(4096, model_config.max_tokens),  # 使用合理的max_tokens值
                openai_api_base=self.config.base_url,
                openai_api_key=self.config.api_key or "dummy-key",
                streaming=True
            )

    async def chat_with_tools(
        self,
        messages: List[BaseMessage],
        tools: List[LangChainBaseTool],
        model_name: Optional[str] = None,
        stream: bool = False
    ) -> Union[AIMessage, AsyncGenerator[str, None]]:
        """
        使用工具进行聊天

        Args:
            messages: 消息列表
            tools: 工具列表
            model_name: 模型名称
            stream: 是否流式响应

        Returns:
            Union[AIMessage, AsyncGenerator[str, None]]: 响应消息或流式生成器
        """
        try:
            # 获取模型
            if not model_name:
                model_name = self.config.default_model

            model = self._get_langchain_model(model_name)

            # 绑定工具
            if tools:
                model_with_tools = model.bind_tools(tools)
            else:
                model_with_tools = model

            # 调用模型
            if stream:
                # 流式响应
                async def stream_generator():
                    async for chunk in model_with_tools.astream(messages):
                        if hasattr(chunk, 'content') and chunk.content:
                            yield chunk.content

                return stream_generator()
            else:
                # 非流式响应
                response = await model_with_tools.ainvoke(messages)
                return response

        except Exception as e:
            logger.error(f"工具聊天失败: {e}")
            error_message = AIMessage(content=f"抱歉，处理您的请求时出现了错误: {str(e)}")

            if stream:
                async def error_generator():
                    yield error_message.content
                return error_generator()
            else:
                return error_message

    def convert_messages_to_langchain(self, messages: List[ChatMessage]) -> List[BaseMessage]:
        """
        将ChatMessage转换为LangChain BaseMessage

        Args:
            messages: ChatMessage列表

        Returns:
            List[BaseMessage]: LangChain消息列表
        """
        langchain_messages = []

        for msg in messages:
            if msg.role == "user":
                langchain_messages.append(HumanMessage(content=msg.content))
            elif msg.role == "assistant":
                langchain_messages.append(AIMessage(content=msg.content))
            elif msg.role == "system":
                langchain_messages.append(SystemMessage(content=msg.content))

        return langchain_messages

    def convert_langchain_to_messages(self, langchain_messages: List[BaseMessage]) -> List[ChatMessage]:
        """
        将LangChain BaseMessage转换为ChatMessage

        Args:
            langchain_messages: LangChain消息列表

        Returns:
            List[ChatMessage]: ChatMessage列表
        """
        messages = []

        for msg in langchain_messages:
            if isinstance(msg, HumanMessage):
                messages.append(ChatMessage(role="user", content=msg.content))
            elif isinstance(msg, AIMessage):
                messages.append(ChatMessage(role="assistant", content=msg.content))
            elif isinstance(msg, SystemMessage):
                messages.append(ChatMessage(role="system", content=msg.content))

        return messages
