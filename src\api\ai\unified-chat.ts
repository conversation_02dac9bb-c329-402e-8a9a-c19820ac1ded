/**
 * 统一聊天API接口
 * 
 * 集成AI聊天和智能体功能的统一接口
 */
import { fastApiRequest } from '/@/config/knowledgeBase';
import { Session } from '/@/utils/storage';

// ==================== 数据类型定义 ====================

/**
 * 统一聊天请求参数
 */
export interface UnifiedChatRequest {
  message: string;
  session_id?: string;
  mode?: 'auto' | 'ai_only' | 'agent';
  model?: string;
  temperature?: number;
  max_tokens?: number;
  stream?: boolean;
  agent_config?: AgentConfig;
  context?: Record<string, any>;
}

/**
 * 智能体配置
 */
export interface AgentConfig {
  max_tool_calls?: number;
  tool_selection_strategy?: string;
  enable_memory?: boolean;
  memory_window?: number;
}

/**
 * 工具调用信息
 */
export interface ToolCallInfo {
  tool_name: string;
  success: boolean;
  execution_time: number;
  outputs?: Record<string, any>;
  error_message?: string;
}

/**
 * 统一聊天响应
 */
export interface UnifiedChatResponse {
  session_id: string;
  message_id: string;
  content: string;
  mode_used: string;
  model_used: string;
  tool_calls: ToolCallInfo[];
  execution_summary?: string;
  response_time: number;
  token_usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  timestamp: string;
}

/**
 * 流式聊天事件
 */
export interface StreamChatEvent {
  event: 'start' | 'delta' | 'tool_call' | 'response' | 'message' | 'end' | 'error' |
         'node_start' | 'node_complete' | 'thinking' | 'tool_execution' | 'completion';
  session_id?: string;
  content?: string;
  data?: any;
  error?: string;
  streamed?: boolean;  // 标识是否为流式模式
  summary?: string;    // 执行摘要

  // 新增字段支持后台事件格式
  type?: string;       // 事件类型
  node?: string;       // 节点名称
  content_type?: string; // 内容类型
  tool_name?: string;  // 工具名称
  tool_args?: Record<string, any>; // 工具参数
  tool_result?: any;   // 工具结果
  status?: 'pending' | 'success' | 'error' | 'loading'; // 工具执行状态
}

// ==================== 工具函数 ====================

/**
 * 获取认证token
 */
export function getAuthorizationHeader(): string {
  const token = Session.get('token');
  if (!token) {
    return '';
  }
  return token.startsWith('Bearer ') ? token : `Bearer ${token}`;
}

// ==================== API接口函数 ====================

/**
 * 统一聊天接口
 * @param params 聊天请求参数
 * @returns 聊天响应
 */
export async function unifiedChat(params: UnifiedChatRequest): Promise<UnifiedChatResponse> {
  try {
    const authHeader = getAuthorizationHeader();
    if (!authHeader) {
      throw new Error('用户未登录，请先登录后再试');
    }

    const response = await fastApiRequest.post('/api/iot/v1/chat/chat', params);
    return response.data;
  } catch (error: any) {
    console.error('统一聊天失败:', error);
    if (error.response?.status === 401) {
      throw new Error('认证失败，请重新登录');
    }
    throw new Error(error.response?.data?.detail || '聊天失败');
  }
}

/**
 * 统一流式聊天接口
 * @param params 聊天请求参数
 * @param onEvent 事件回调函数
 * @param onError 错误回调函数
 * @param onComplete 完成回调函数
 */
export async function unifiedChatStream(
  params: UnifiedChatRequest,
  onEvent: (event: StreamChatEvent) => void,
  onError: (error: Error) => void,
  onComplete: () => void
): Promise<void> {
  try {
    const authHeader = getAuthorizationHeader();
    if (!authHeader) {
      throw new Error('用户未登录，请先登录后再试');
    }

    const response = await fetch(`${fastApiRequest.defaults.baseURL}/api/iot/v1/chat/chat/stream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader,
      },
      body: JSON.stringify(params),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('无法获取响应流');
    }

    const decoder = new TextDecoder();
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          break;
        }

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            
            try {
              const event: StreamChatEvent = JSON.parse(data);
              onEvent(event);
              
              if (event.event === 'end') {
                onComplete();
                return;
              }
            } catch (parseError) {
              console.warn('解析流式响应失败:', parseError);
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

    onComplete();
  } catch (error: any) {
    console.error('统一流式聊天失败:', error);
    onError(new Error(error.message || '流式聊天失败'));
  }
}

/**
 * 生成会话ID
 */
export function generateSessionId(): string {
  return `session_${Date.now()}_${Math.random().toString(36).substring(2)}`;
}

/**
 * 格式化工具调用信息
 */
export function formatToolCalls(toolCalls: ToolCallInfo[]): string {
  if (!toolCalls || toolCalls.length === 0) {
    return '未使用工具';
  }

  const successCount = toolCalls.filter(call => call.success).length;
  const totalTime = toolCalls.reduce((sum, call) => sum + call.execution_time, 0);
  
  return `使用了 ${toolCalls.length} 个工具（${successCount} 个成功），总耗时 ${totalTime.toFixed(2)}秒`;
}

/**
 * 获取模式显示名称
 */
export function getModeDisplayName(mode: string): string {
  const modeNames: Record<string, string> = {
    'auto': '自动模式',
    'ai_only': 'AI对话',
    'agent': '智能体模式'
  };
  return modeNames[mode] || mode;
}

/**
 * 判断是否为工具调用响应
 */
export function hasToolCalls(response: UnifiedChatResponse): boolean {
  return response.tool_calls && response.tool_calls.length > 0;
}

/**
 * 获取响应类型标签
 */
export function getResponseTypeTag(response: UnifiedChatResponse): {
  text: string;
  type: 'primary' | 'success' | 'warning' | 'info';
} {
  if (hasToolCalls(response)) {
    const successCount = response.tool_calls.filter(call => call.success).length;
    const totalCount = response.tool_calls.length;
    
    if (successCount === totalCount) {
      return { text: '智能体', type: 'success' };
    } else if (successCount > 0) {
      return { text: '部分成功', type: 'warning' };
    } else {
      return { text: '工具失败', type: 'warning' };
    }
  } else {
    return { text: 'AI对话', type: 'primary' };
  }
}

/**
 * 创建默认聊天请求
 */
export function createDefaultChatRequest(message: string, sessionId?: string): UnifiedChatRequest {
  return {
    message,
    session_id: sessionId,
    mode: 'auto',
    model: 'Qwen2.5-72B',
    temperature: 0.7,
    stream: false,
    agent_config: {
      max_tool_calls: 5,
      enable_memory: true,
      memory_window: 10
    }
  };
}

/**
 * 创建流式聊天请求
 */
export function createStreamChatRequest(message: string, sessionId?: string): UnifiedChatRequest {
  return {
    ...createDefaultChatRequest(message, sessionId),
    stream: true
  };
}

// ==================== 错误处理 ====================

/**
 * 聊天错误类型
 */
export class ChatError extends Error {
  constructor(
    message: string,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'ChatError';
  }
}

/**
 * 处理聊天错误
 */
export function handleChatError(error: any): ChatError {
  if (error instanceof ChatError) {
    return error;
  }

  if (error.response) {
    const status = error.response.status;
    const detail = error.response.data?.detail || error.message;
    
    switch (status) {
      case 401:
        return new ChatError('认证失败，请重新登录', 'AUTH_FAILED');
      case 403:
        return new ChatError('权限不足', 'PERMISSION_DENIED');
      case 429:
        return new ChatError('请求过于频繁，请稍后重试', 'RATE_LIMITED');
      case 500:
        return new ChatError('服务器内部错误', 'SERVER_ERROR', detail);
      default:
        return new ChatError(detail || '请求失败', 'REQUEST_FAILED');
    }
  }

  return new ChatError(error.message || '未知错误', 'UNKNOWN_ERROR');
}
