#!/usr/bin/env python3
"""
统一流式处理核心服务
整合所有流式处理逻辑，消除重复代码
"""

from typing import Dict, Any, Optional, AsyncGenerator, List, Set, Union
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
import uuid
import asyncio
from loguru import logger

from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
from langchain_core.tools import BaseTool


@dataclass
class StreamEvent:
    """统一的流式事件格式"""
    type: str  # 事件类型：thinking, delta, tool_execution, node_start, node_complete, completion, error
    content: str = ""
    node: str = "unknown"
    content_type: str = "response"  # response, thinking
    tool_name: Optional[str] = None
    tool_args: Optional[Dict[str, Any]] = None
    tool_result: Optional[Any] = None
    status: Optional[str] = None  # pending, success, error
    metadata: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {
            "type": self.type,
            "content": self.content,
            "node": self.node
        }
        
        if self.content_type != "response":
            result["content_type"] = self.content_type
            
        if self.tool_name:
            result["tool_name"] = self.tool_name
            
        if self.tool_args:
            result["tool_args"] = self.tool_args
            
        if self.tool_result:
            result["tool_result"] = self.tool_result
            
        if self.status:
            result["status"] = self.status
            
        if self.metadata:
            result.update(self.metadata)
            
        return result


class StreamingCoreService:
    """统一的流式处理核心服务"""
    
    def __init__(self):
        self.node_accumulated_content: Dict[str, str] = {}
        self.current_node: Optional[str] = None
        self.processed_tool_calls: Set[str] = set()
    
    def reset_state(self):
        """重置流式处理状态"""
        self.node_accumulated_content.clear()
        self.current_node = None
        self.processed_tool_calls.clear()
    
    async def process_token_stream(
        self,
        token_content: str,
        node_name: str,
        accumulated_content: str = ""
    ) -> Optional[StreamEvent]:
        """
        处理逐token流式内容
        
        Args:
            token_content: 当前token内容
            node_name: 节点名称
            accumulated_content: 已累积的内容
            
        Returns:
            StreamEvent: 处理后的流式事件，如果无新内容则返回None
        """
        # 只有当内容真正增加时才处理
        if token_content != accumulated_content and len(token_content) > len(accumulated_content):
            if token_content.startswith(accumulated_content):
                # 正常的增量内容
                new_token = token_content[len(accumulated_content):]
                
                if new_token:  # 确保有新内容
                    # 更新累积内容
                    self.node_accumulated_content[node_name] = token_content
                    
                    # 判断内容类型
                    full_content = token_content
                    
                    # 检查是否在thinking标签内
                    in_thinking = "<think>" in full_content and "</think>" not in full_content
                    is_thinking_token = (
                        "<think>" in new_token or 
                        "</think>" in new_token or 
                        in_thinking
                    )
                    
                    if is_thinking_token:
                        # 思考内容
                        return StreamEvent(
                            type="thinking",
                            content=new_token,
                            node=node_name,
                            content_type="thinking"
                        )
                    else:
                        # 响应内容
                        return StreamEvent(
                            type="delta",
                            content=new_token,
                            node=node_name,
                            content_type="response"
                        )
        
        return None
    
    def create_node_event(self, event_type: str, node_name: str) -> StreamEvent:
        """创建节点事件"""
        return StreamEvent(
            type=event_type,
            node=node_name,
            content=""
        )
    
    def create_tool_event(
        self,
        tool_name: str,
        node_name: str,
        status: str,
        tool_args: Optional[Dict[str, Any]] = None,
        tool_result: Optional[Any] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> StreamEvent:
        """创建工具执行事件"""
        return StreamEvent(
            type="tool_execution",
            node=node_name,
            tool_name=tool_name,
            tool_args=tool_args or {},
            tool_result=tool_result,
            status=status,
            metadata=metadata or {}
        )
    
    def create_completion_event(self) -> StreamEvent:
        """创建完成事件"""
        return StreamEvent(
            type="completion",
            content="会话完成"
        )
    
    def create_error_event(self, error_message: str, node_name: str = "system") -> StreamEvent:
        """创建错误事件"""
        return StreamEvent(
            type="error",
            content=error_message,
            node=node_name,
            status="error"
        )
    
    def extract_tool_calls_from_update(self, update_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        从更新数据中提取工具调用信息
        
        Args:
            update_data: 更新数据
            
        Returns:
            List[Dict[str, Any]]: 工具调用列表
        """
        tool_calls = []
        
        # 检查是否有工具调用信息
        if isinstance(update_data, dict):
            # 检查messages中的tool_calls
            messages = update_data.get("messages", [])
            if messages:
                last_message = messages[-1] if isinstance(messages, list) else messages
                if hasattr(last_message, "tool_calls") and last_message.tool_calls:
                    for tool_call in last_message.tool_calls:
                        tool_calls.append({
                            "tool_name": tool_call.get("name", "unknown"),
                            "tool_display_name": tool_call.get("name", "unknown"),
                            "inputs": tool_call.get("args", {}),
                            "status": "pending",
                            "start_time": datetime.now().isoformat(),
                            "metadata": {"tool_call_id": tool_call.get("id", str(uuid.uuid4()))}
                        })
            
            # 检查tool_results中的结果
            tool_results = update_data.get("tool_results", [])
            if tool_results:
                for result in tool_results:
                    if isinstance(result, dict):
                        tool_calls.append({
                            "tool_name": result.get("tool_name", "unknown"),
                            "tool_display_name": result.get("tool_name", "unknown"),
                            "inputs": {},
                            "outputs": {"result": result.get("result", "")},
                            "status": "success",
                            "end_time": datetime.now().isoformat(),
                            "metadata": {"tool_call_id": result.get("tool_call_id", str(uuid.uuid4()))}
                        })
        
        logger.debug(f"从节点更新中提取到 {len(tool_calls)} 个工具调用")
        return tool_calls


class StreamingServiceMixin:
    """流式处理服务混入类"""
    
    def __init__(self):
        self.streaming_core = StreamingCoreService()
    
    async def emit_stream_event(self, event: StreamEvent) -> Dict[str, Any]:
        """发送流式事件"""
        return event.to_dict()
    
    async def handle_stream_error(self, error: Exception, context: str = "") -> StreamEvent:
        """处理流式错误"""
        error_message = f"{context}: {str(error)}" if context else str(error)
        logger.error(f"流式处理错误: {error_message}")
        return self.streaming_core.create_error_event(error_message)
