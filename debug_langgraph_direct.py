#!/usr/bin/env python3
"""
直接测试LangGraph的messages流式模式
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.app.iot.service.langgraph_agent_service import LangGraphAgentService
from langchain_core.messages import HumanMessage
from loguru import logger

async def debug_direct_messages():
    """直接测试LangGraph的messages流式模式"""
    print("🔍 直接测试LangGraph messages流式模式")
    
    try:
        # 创建服务实例
        service = LangGraphAgentService()
        
        # 初始化服务
        print("📋 初始化LangGraph服务...")
        await service.initialize()
        print("✅ LangGraph服务初始化成功")
        
        # 创建初始状态
        initial_state = {
            "messages": [HumanMessage(content="计算3+2")],
            "session_id": "debug_direct_001",
            "user_id": 1,
            "tool_results": [],
            "execution_metadata": {
                "start_time": "2025-09-10T17:00:00",
                "execution_id": "test-direct-001"
            }
        }
        
        # 配置运行参数
        run_config = {
            "configurable": {
                "thread_id": "debug_direct_001",
                "user_id": 1
            }
        }
        
        print("\n🧪 测试直接messages流式模式...")
        print("=" * 60)
        
        event_count = 0
        
        # 直接使用graph.astream with messages模式
        async for stream_mode, chunk in service.graph.astream(
            initial_state,
            stream_mode=["messages"],  # 只使用messages模式
            config=run_config
        ):
            event_count += 1
            
            if stream_mode == "messages":
                if isinstance(chunk, tuple) and len(chunk) == 2:
                    message_chunk, metadata = chunk
                    node_name = metadata.get("langgraph_node", "unknown")
                    
                    if hasattr(message_chunk, "content") and message_chunk.content is not None:
                        content = message_chunk.content
                        print(f"[{event_count:03d}] Node: {node_name}")
                        print(f"      Token: {repr(content)}")
                        print(f"      Length: {len(content)}")
                        print("-" * 40)
        
        print(f"\n📊 总事件数: {event_count}")
        
    except Exception as e:
        logger.error(f"调试失败: {e}")
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_direct_messages())
