🚀 开始LangGraph流式处理测试
📝 测试时间: 2025-09-10 16:56:29
📄 输出文件: langgraph_streaming_test_20250910_165629.txt


============================================================
🧪 测试 stream_mode='updates' (节点状态更新)
============================================================
📝 用户消息: 计算 3 + 2 * 5

🔄 流式输出 (updates模式):
Update: {'agent': {'messages': [AIMessage(content='<think>\n好的，我现在需要解决用户的问题：计算3 + 2 * 5。首先，我应该确定用户需要的是数学运算，所以应该使用计算器工具。根据数学运算的优先级，乘法应该先于加法进行。所以，先算2乘以5等于10，然后再加上3，得到13。我需要确认计算器函数是否能正确处理这样的表达式。用户提供的表达式是3 + 2 * 5，直接传递给calculator函数的expression参数即可。不需要其他步骤，直接调用工具计算结果。然后将结果返回给用户。\n</think>\n\n', additional_kwargs={'tool_calls': [{'index': 0, 'id': 'chatcmpl-tool-3ff5479a2b67462aa303b387139c856a', 'function': {'arguments': '{"expression": "3 + 2 * 5"}', 'name': 'calculator'}, 'type': 'function'}]}, response_metadata={'finish_reason': 'tool_calls', 'model_name': 'Qwen3-32B-AWQ'}, id='run--88fad898-605d-4009-b2c0-d42192849cc4-0', tool_calls=[{'name': 'calculator', 'args': {'expression': '3 + 2 * 5'}, 'id': 'chatcmpl-tool-3ff5479a2b67462aa303b387139c856a', 'type': 'tool_call'}])]}}
----------------------------------------
Update: {'tools': {'messages': [ToolMessage(content='计算结果: 13', name='calculator', id='752d7a0a-2df0-4fac-b375-44c934518af4', tool_call_id='chatcmpl-tool-3ff5479a2b67462aa303b387139c856a')]}}
----------------------------------------
Update: {'agent': {'messages': [AIMessage(content='<think>\n好的，用户让我计算3加2乘以5。首先，我需要确定运算的优先级。根据数学规则，乘法应该先于加法进行。所以，我应该先算2乘以5，得到10，然后再加上3，结果就是13。为了确保正确，我调用了计算器工具来验证这个表达式。计算器返回的结果确实是13，所以最终答案应该是13。用户可能是在测试我的运算顺序理解是否正确，或者需要确认自己的计算结果。总之，答案正确，无需进一步操作。\n</think>\n\n3 + 2 × 5 的计算结果是 **13**。  \n（根据数学运算优先级，先进行乘法 2×5=10，再执行加法 3+10=13）', additional_kwargs={}, response_metadata={'finish_reason': 'stop', 'model_name': 'Qwen3-32B-AWQ'}, id='run--6eac0919-3d1a-4646-a499-f2334d0a629e-0')]}}
----------------------------------------

============================================================
🧪 测试 stream_mode='messages' (LLM token流)
============================================================
📝 用户消息: 计算 3 + 2 * 5

🔄 流式输出 (messages模式):
Token: content='' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='<think>' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='\n' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='好的' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='用户' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='让我' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='计算' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='3' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content=' +' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content=' ' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='2' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content=' *' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content=' ' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='5' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='。' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='首先' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='我' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='需要' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='确定' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='数学' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='运算' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='的' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='优先' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='级' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='。' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='根据' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='运算' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='规则' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='乘' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='法' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='应该' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='先' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='于' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='加' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='法' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='进行' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='。' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='所以' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='先' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='算' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='2' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='乘' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='以' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='5' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='得到' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='1' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='0' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='然后' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='再加上' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='3' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='结果' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='就是' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='1' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='3' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='。' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='这里' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='不需要' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='使用' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='括' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='号' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='因为' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='默认' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='的' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='运算' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='顺序' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='已经' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='正确' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='。' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='我' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='应该' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='使用' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='计算器' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='工具' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='来' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='确认' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='这个' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='结果' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='是否' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='正确' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='。' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='调' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='用' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='计算器' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='函数' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='传' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='入' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='表达' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='式' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='3' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content=' +' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content=' ' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='2' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content=' *' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content=' ' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='5' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='应该' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='能得到' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='1' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='3' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='的答案' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='。' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='用户' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='可能' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='是在' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='测试' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='我' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='是否' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='了解' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='运算' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='优先' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='级' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='或者' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='真的' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='需要' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='这个' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='计算' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='结果' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='。' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='确保' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='回答' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='清晰' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='说明' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='步骤' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='让用户' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='明白' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='过程' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='。\n' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='</think>' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='\n\n' additional_kwargs={} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='' additional_kwargs={'tool_calls': [{'index': 0, 'id': 'chatcmpl-tool-1e1f186c64ec40bb8cda5258f6ba4602', 'function': {'arguments': None, 'name': 'calculator'}, 'type': 'function'}]} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0' tool_calls=[{'name': 'calculator', 'args': {}, 'id': 'chatcmpl-tool-1e1f186c64ec40bb8cda5258f6ba4602', 'type': 'tool_call'}] tool_call_chunks=[{'name': 'calculator', 'args': None, 'id': 'chatcmpl-tool-1e1f186c64ec40bb8cda5258f6ba4602', 'index': 0, 'type': 'tool_call_chunk'}]
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='' additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': '{"expression": "', 'name': None}, 'type': None}]} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0' tool_calls=[{'name': '', 'args': {'expression': ''}, 'id': None, 'type': 'tool_call'}] tool_call_chunks=[{'name': None, 'args': '{"expression": "', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='' additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': '3', 'name': None}, 'type': None}]} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0' invalid_tool_calls=[{'name': None, 'args': '3', 'id': None, 'error': None, 'type': 'invalid_tool_call'}] tool_call_chunks=[{'name': None, 'args': '3', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='' additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': ' +', 'name': None}, 'type': None}]} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0' invalid_tool_calls=[{'name': None, 'args': ' +', 'id': None, 'error': None, 'type': 'invalid_tool_call'}] tool_call_chunks=[{'name': None, 'args': ' +', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='' additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': ' ', 'name': None}, 'type': None}]} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0' invalid_tool_calls=[{'name': None, 'args': ' ', 'id': None, 'error': None, 'type': 'invalid_tool_call'}] tool_call_chunks=[{'name': None, 'args': ' ', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='' additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': '2', 'name': None}, 'type': None}]} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0' invalid_tool_calls=[{'name': None, 'args': '2', 'id': None, 'error': None, 'type': 'invalid_tool_call'}] tool_call_chunks=[{'name': None, 'args': '2', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='' additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': ' *', 'name': None}, 'type': None}]} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0' invalid_tool_calls=[{'name': None, 'args': ' *', 'id': None, 'error': None, 'type': 'invalid_tool_call'}] tool_call_chunks=[{'name': None, 'args': ' *', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='' additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': ' ', 'name': None}, 'type': None}]} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0' invalid_tool_calls=[{'name': None, 'args': ' ', 'id': None, 'error': None, 'type': 'invalid_tool_call'}] tool_call_chunks=[{'name': None, 'args': ' ', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='' additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': '5', 'name': None}, 'type': None}]} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0' invalid_tool_calls=[{'name': None, 'args': '5', 'id': None, 'error': None, 'type': 'invalid_tool_call'}] tool_call_chunks=[{'name': None, 'args': '5', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='' additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': '"}', 'name': None}, 'type': None}]} response_metadata={} id='run--2174fa11-524b-4b4b-8a02-710550c405d0' invalid_tool_calls=[{'name': None, 'args': '"}', 'id': None, 'error': None, 'type': 'invalid_tool_call'}] tool_call_chunks=[{'name': None, 'args': '"}', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='' additional_kwargs={} response_metadata={'finish_reason': 'tool_calls', 'model_name': 'Qwen3-32B-AWQ'} id='run--2174fa11-524b-4b4b-8a02-710550c405d0'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'checkpoint_ns': 'agent:ac6d4e17-db4d-549d-0071-34444779ed37', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='计算结果: 13' name='calculator' id='a156df62-dd43-48b8-ab10-0b0932b6a375' tool_call_id='chatcmpl-tool-1e1f186c64ec40bb8cda5258f6ba4602'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 2, 'langgraph_node': 'tools', 'langgraph_triggers': ('branch:to:tools',), 'langgraph_path': ('__pregel_pull', 'tools'), 'langgraph_checkpoint_ns': 'tools:95e1ec85-632e-ca4d-9666-98d47a30deac'}
----------------------------------------
Token: content='' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='<think>' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='\n' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='好的' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='用户' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='让我' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='计算' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='3' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='加' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='2' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='乘' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='以' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='5' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='。' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='首先' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='我' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='需要' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='确认' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='运算' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='顺序' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='。' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='根据' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='数学' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='规则' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='乘' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='法' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='优先' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='于' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='加' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='法' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='所以' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='先' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='算' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='2' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='乘' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='5' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='等于' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='1' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='0' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='再加上' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='3' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='结果' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='是' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='1' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='3' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='。' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='然后' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='我' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='调' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='用' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='计算器' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='工具' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='来' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='验证' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='表达' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='式' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='输入' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='正确' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='结果' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='也是' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='1' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='3' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='。' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='用户' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='可能' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='是在' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='测试' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='我' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='是否' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='知道' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='运算' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='优先' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='级' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='或者' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='真的' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='需要' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='这个' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='结果' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='。' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='回答' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='时' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='要' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='清楚' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='说明' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='步骤' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='让用户' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='明白' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='过程' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='。' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='所以' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='最终' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='答案' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='就是' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='1' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='3' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='。\n\n' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='</think>' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='\n\n' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='3' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content=' +' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content=' ' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='2' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content=' ×' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content=' ' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='5' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content=' 的' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='计算' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='结果' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='是' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content=' **' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='1' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='3' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='**' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='。\n\n' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='计算' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='过程' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='遵循' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='数学' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='运算' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='优先' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='级' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='：\n' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='1' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='.' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content=' 先' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='进行' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='乘' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='法' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='运算' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='：' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='2' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content=' ×' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content=' ' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='5' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content=' =' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content=' ' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='1' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='0' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='\n' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='2' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='.' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content=' 再' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='进行' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='加' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='法' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='运算' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='：' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='3' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content=' +' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content=' ' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='1' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='0' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content=' =' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content=' ' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='1' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='3' additional_kwargs={} response_metadata={} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='' additional_kwargs={} response_metadata={'finish_reason': 'stop', 'model_name': 'Qwen3-32B-AWQ'} id='run--1634f30c-70d0-4e91-8fa9-0548ef6e906d'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'checkpoint_ns': 'agent:5b8e7eb3-b996-4b67-581e-26ca4ccbc467', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------

============================================================
🧪 测试 stream_mode='custom' (自定义数据流)
============================================================
📝 用户消息: 计算 3 + 2 * 5

🔄 流式输出 (custom模式):
Custom: {'type': 'tool_progress', 'message': '开始计算: 3 + 2 * 5'}
----------------------------------------
Custom: {'type': 'tool_progress', 'message': '计算完成，结果: 13'}
----------------------------------------

============================================================
🧪 测试多模式组合 ['messages', 'updates', 'custom']
============================================================
📝 用户消息: 计算 3 + 2 * 5

🔄 流式输出 (多模式):
Mode: messages
Chunk: (AIMessageChunk(content='', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='<think>', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='\n', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='好的', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='用户', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='让我', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='计算', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='3', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' +', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' ', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='2', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' *', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' ', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='5', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='。', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='首先', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='我', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='需要', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='确定', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='这个', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='数学', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='表达', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='式的', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='正确', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='顺序', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='。', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='根据', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='数学', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='运算', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='的', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='优先', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='级', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='乘', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='法', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='应该', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='先', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='于', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='加', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='法', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='进行', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='。', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='所以', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='先', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='算', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='2', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='乘', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='以', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='5', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='得到', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='1', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='0', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='然后', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='再加上', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='3', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='结果', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='就是', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='1', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='3', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='。', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='这里', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='不需要', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='使用', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='括', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='号', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='因为', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='默认', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='的', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='运算', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='顺序', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='已经', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='能', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='正确', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='处理', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='这个', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='表达', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='式', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='。', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='我', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='应该', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='调', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='用', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='计算器', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='工具', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='来', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='验证', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='这个', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='结果', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='确保', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='没有', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='计算', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='错误', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='。', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='然后', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='把', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='最终', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='答案', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='返回', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='给', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='用户', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='。\n', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='</think>', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='\n\n', additional_kwargs={}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='', additional_kwargs={'tool_calls': [{'index': 0, 'id': 'chatcmpl-tool-04c2b42445e74e599965127eb0734685', 'function': {'arguments': None, 'name': 'calculator'}, 'type': 'function'}]}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea', tool_calls=[{'name': 'calculator', 'args': {}, 'id': 'chatcmpl-tool-04c2b42445e74e599965127eb0734685', 'type': 'tool_call'}], tool_call_chunks=[{'name': 'calculator', 'args': None, 'id': 'chatcmpl-tool-04c2b42445e74e599965127eb0734685', 'index': 0, 'type': 'tool_call_chunk'}]), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='', additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': '{"expression": "', 'name': None}, 'type': None}]}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea', tool_calls=[{'name': '', 'args': {'expression': ''}, 'id': None, 'type': 'tool_call'}], tool_call_chunks=[{'name': None, 'args': '{"expression": "', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='', additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': '3', 'name': None}, 'type': None}]}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea', invalid_tool_calls=[{'name': None, 'args': '3', 'id': None, 'error': None, 'type': 'invalid_tool_call'}], tool_call_chunks=[{'name': None, 'args': '3', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='', additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': ' +', 'name': None}, 'type': None}]}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea', invalid_tool_calls=[{'name': None, 'args': ' +', 'id': None, 'error': None, 'type': 'invalid_tool_call'}], tool_call_chunks=[{'name': None, 'args': ' +', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='', additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': ' ', 'name': None}, 'type': None}]}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea', invalid_tool_calls=[{'name': None, 'args': ' ', 'id': None, 'error': None, 'type': 'invalid_tool_call'}], tool_call_chunks=[{'name': None, 'args': ' ', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='', additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': '2', 'name': None}, 'type': None}]}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea', invalid_tool_calls=[{'name': None, 'args': '2', 'id': None, 'error': None, 'type': 'invalid_tool_call'}], tool_call_chunks=[{'name': None, 'args': '2', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='', additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': ' *', 'name': None}, 'type': None}]}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea', invalid_tool_calls=[{'name': None, 'args': ' *', 'id': None, 'error': None, 'type': 'invalid_tool_call'}], tool_call_chunks=[{'name': None, 'args': ' *', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='', additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': ' ', 'name': None}, 'type': None}]}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea', invalid_tool_calls=[{'name': None, 'args': ' ', 'id': None, 'error': None, 'type': 'invalid_tool_call'}], tool_call_chunks=[{'name': None, 'args': ' ', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='', additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': '5', 'name': None}, 'type': None}]}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea', invalid_tool_calls=[{'name': None, 'args': '5', 'id': None, 'error': None, 'type': 'invalid_tool_call'}], tool_call_chunks=[{'name': None, 'args': '5', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='', additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': '"}', 'name': None}, 'type': None}]}, response_metadata={}, id='run--a01be98e-90a0-433b-a979-d1567738baea', invalid_tool_calls=[{'name': None, 'args': '"}', 'id': None, 'error': None, 'type': 'invalid_tool_call'}], tool_call_chunks=[{'name': None, 'args': '"}', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='', additional_kwargs={}, response_metadata={'finish_reason': 'tool_calls', 'model_name': 'Qwen3-32B-AWQ'}, id='run--a01be98e-90a0-433b-a979-d1567738baea'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'checkpoint_ns': 'agent:2b23475f-2904-28fb-dbc6-8d4481997645', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: updates
Chunk: {'agent': {'messages': [AIMessage(content='<think>\n好的，用户让我计算3 + 2 * 5。首先，我需要确定这个数学表达式的正确顺序。根据数学运算的优先级，乘法应该先于加法进行。所以，先算2乘以5，得到10，然后再加上3，结果就是13。这里不需要使用括号，因为默认的运算顺序已经能正确处理这个表达式。我应该调用计算器工具来验证这个结果，确保没有计算错误。然后，把最终答案返回给用户。\n</think>\n\n', additional_kwargs={'tool_calls': [{'index': 0, 'id': 'chatcmpl-tool-04c2b42445e74e599965127eb0734685', 'function': {'arguments': '{"expression": "3 + 2 * 5"}', 'name': 'calculator'}, 'type': 'function'}]}, response_metadata={'finish_reason': 'tool_calls', 'model_name': 'Qwen3-32B-AWQ'}, id='run--a01be98e-90a0-433b-a979-d1567738baea', tool_calls=[{'name': 'calculator', 'args': {'expression': '3 + 2 * 5'}, 'id': 'chatcmpl-tool-04c2b42445e74e599965127eb0734685', 'type': 'tool_call'}])]}}
----------------------------------------
Mode: custom
Chunk: {'type': 'tool_progress', 'message': '开始计算: 3 + 2 * 5'}
----------------------------------------
Mode: custom
Chunk: {'type': 'tool_progress', 'message': '计算完成，结果: 13'}
----------------------------------------
Mode: messages
Chunk: (ToolMessage(content='计算结果: 13', name='calculator', id='33b0ab58-d401-4b17-a16a-6e2397fd85ba', tool_call_id='chatcmpl-tool-04c2b42445e74e599965127eb0734685'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 2, 'langgraph_node': 'tools', 'langgraph_triggers': ('branch:to:tools',), 'langgraph_path': ('__pregel_pull', 'tools'), 'langgraph_checkpoint_ns': 'tools:45e23a99-decb-e587-d299-6f0951cdece5'})
----------------------------------------
Mode: updates
Chunk: {'tools': {'messages': [ToolMessage(content='计算结果: 13', name='calculator', id='33b0ab58-d401-4b17-a16a-6e2397fd85ba', tool_call_id='chatcmpl-tool-04c2b42445e74e599965127eb0734685')]}}
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='<think>', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='\n', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='好的', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='用户', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='让我', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='计算', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='3', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='加', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='2', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='乘', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='以', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='5', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='。', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='首先', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='我', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='需要', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='确认', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='运算', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='顺序', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='。', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='根据', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='数学', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='规则', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='乘', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='法', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='优先', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='于', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='加', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='法', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='所以', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='应该', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='先', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='算', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='2', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='乘', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='5', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='等于', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='1', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='0', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='然后再', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='加', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='3', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='得到', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='1', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='3', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='。', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='为了', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='确保', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='正确', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='我', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='可以用', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='计算器', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='工具', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='来', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='验证', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='。', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='调', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='用', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='计算器', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='函数', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='输入', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='表达', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='式', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='3', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' +', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' ', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='2', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' *', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' ', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='5', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='结果', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='确实是', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='1', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='3', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='。', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='所以', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='最终', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='答案', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='就是', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='1', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='3', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='不需要', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='其他', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='操作', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='。\n', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='</think>', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='\n\n', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='根据', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='数学', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='运算', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='规则', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='先', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='进行', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='乘', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='法', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='再', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='进行', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='加', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='法', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='运算', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='：\n\n', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='3', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' +', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' (', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='2', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' ×', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' ', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='5', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=')', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' =', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' ', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='3', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' +', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' ', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='1', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='0', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' =', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' ', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='1', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='3', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='\n\n', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='最终', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='计算', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='结果', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='为', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='：', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='1', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='3', additional_kwargs={}, response_metadata={}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='', additional_kwargs={}, response_metadata={'finish_reason': 'stop', 'model_name': 'Qwen3-32B-AWQ'}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'checkpoint_ns': 'agent:b859056f-031c-d9a6-78e3-67541e5ae8e0', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: updates
Chunk: {'agent': {'messages': [AIMessage(content='<think>\n好的，用户让我计算3加2乘以5。首先，我需要确认运算顺序。根据数学规则，乘法优先于加法，所以应该先算2乘5等于10，然后再加3，得到13。为了确保正确，我可以用计算器工具来验证。调用计算器函数，输入表达式3 + 2 * 5，结果确实是13。所以最终答案就是13，不需要其他操作。\n</think>\n\n根据数学运算规则，先进行乘法再进行加法运算：\n\n3 + (2 × 5) = 3 + 10 = 13\n\n最终计算结果为：13', additional_kwargs={}, response_metadata={'finish_reason': 'stop', 'model_name': 'Qwen3-32B-AWQ'}, id='run--2d45d163-7db1-4385-88e6-c65bae38f04e')]}}
----------------------------------------

✅ 所有测试完成
📄 完整输出已保存到: langgraph_streaming_test_20250910_165629.txt
