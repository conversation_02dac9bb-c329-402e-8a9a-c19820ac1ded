🚀 开始LangGraph流式处理测试
📝 测试时间: 2025-09-10 17:16:28
📄 输出文件: langgraph_streaming_test_20250910_171628.txt


============================================================
🧪 测试 stream_mode='updates' (节点状态更新)
============================================================
📝 用户消息: 计算 3 + 2 * 5

🔄 流式输出 (updates模式):
Update: {'agent': {'messages': [AIMessage(content='<think>\n好的，用户让我计算3 + 2 * 5。首先，我需要确定这个数学表达式的正确顺序。根据数学运算的优先级，乘法应该先于加法进行。所以，先算2乘以5，得到10，然后再加上3，结果是13。这里是否需要使用计算器工具呢？用户提供的工具中有calculator函数，可以计算数学表达式。所以，我应该调用这个工具，把表达式原样传过去，或者按照运算顺序处理后的步骤？可能直接传递原表达式更准确，因为工具应该能正确处理运算顺序。因此，正确的做法是使用calculator函数，参数是3 + 2 * 5，然后返回结果13。不需要其他步骤，因为工具会处理运算顺序的问题。确认一下，如果用户的问题有括号或者其他复杂结构，可能需要先处理，但这里比较简单，直接调用工具即可。\n</think>\n\n', additional_kwargs={'tool_calls': [{'index': 0, 'id': 'chatcmpl-tool-ea8d8e07137f42ae928054cdc17b4d57', 'function': {'arguments': '{"expression": "3 + 2 * 5"}', 'name': 'calculator'}, 'type': 'function'}]}, response_metadata={'finish_reason': 'tool_calls', 'model_name': 'Qwen3-32B-AWQ'}, id='run--051fa071-523f-47a3-bb79-f3c43044bf7a-0', tool_calls=[{'name': 'calculator', 'args': {'expression': '3 + 2 * 5'}, 'id': 'chatcmpl-tool-ea8d8e07137f42ae928054cdc17b4d57', 'type': 'tool_call'}])]}}
----------------------------------------
Update: {'tools': {'messages': [ToolMessage(content='计算结果: 13', name='calculator', id='95242d62-c6ea-4156-93ca-e29bf33ea552', tool_call_id='chatcmpl-tool-ea8d8e07137f42ae928054cdc17b4d57')]}}
----------------------------------------
Update: {'agent': {'messages': [AIMessage(content='<think>\n好的，用户让我计算3加2乘以5。首先，我需要确认运算顺序。根据数学规则，乘法优先于加法，所以应该先算2乘5等于10，再加上3，得到13。然后，我需要使用计算器工具来验证这个结果。调用calculator函数，传入表达式3 + 2 * 5，工具返回的结果是13，和我手动计算的一致。所以最终答案就是13。用户可能是在测试我的运算顺序理解是否正确，或者需要确认自己的计算结果。这时候只需要给出明确的答案，并解释步骤即可。不需要其他工具，因为问题只涉及基本算术运算。\n</think>\n\n计算结果为：**13**\n\n**步骤解释：**\n1. 先进行乘法运算：2 × 5 = 10\n2. 再进行加法运算：3 + 10 = 13', additional_kwargs={}, response_metadata={'finish_reason': 'stop', 'model_name': 'Qwen3-32B-AWQ'}, id='run--fd5a2373-7b65-4270-bc4b-9656ff76f6ac-0')]}}
----------------------------------------

============================================================
🧪 测试 stream_mode='messages' (LLM token流)
============================================================
📝 用户消息: 计算 3 + 2 * 5

🔄 流式输出 (messages模式):
Token: content='' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='<think>' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='\n' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='好的' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='我现在' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='需要' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='处理' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='用户' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='的问题' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='：“' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='计算' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content=' ' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='3' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content=' +' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content=' ' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='2' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content=' *' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content=' ' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='5' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='”。' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='首先' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='用户' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='要求' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='进行' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='数学' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='运算' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='所以我' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='应该' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='使用' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='计算器' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='工具' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='。' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='接下来' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='我' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='需要' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='确定' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='运算' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='的' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='优先' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='级' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='。' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='根据' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='数学' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='规则' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='乘' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='法' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='的' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='优先' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='级' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='高于' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='加' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='法' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='所以' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='应该' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='先' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='计算' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='2' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='乘' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='以' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='5' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='得到' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='1' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='0' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='然后再' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='加上' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='3' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='结果' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='是' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='1' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='3' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='。' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='为了' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='确保' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='正确' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='无' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='误' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='我会' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='调' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='用' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='calculator' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='函数' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，并' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='将' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='表达' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='式' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='原' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='样' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='传递' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='进去' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='这样' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='可以' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='避免' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='任何' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='可能' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='的' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='误解' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='。' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='现在' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='生成' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='相应的' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='工具' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='调' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='用' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='指令' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='。\n' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='</think>' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='\n\n' additional_kwargs={} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='' additional_kwargs={'tool_calls': [{'index': 0, 'id': 'chatcmpl-tool-66acdf49f3cd47c68b9c408e79c5d1e2', 'function': {'arguments': None, 'name': 'calculator'}, 'type': 'function'}]} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df' tool_calls=[{'name': 'calculator', 'args': {}, 'id': 'chatcmpl-tool-66acdf49f3cd47c68b9c408e79c5d1e2', 'type': 'tool_call'}] tool_call_chunks=[{'name': 'calculator', 'args': None, 'id': 'chatcmpl-tool-66acdf49f3cd47c68b9c408e79c5d1e2', 'index': 0, 'type': 'tool_call_chunk'}]
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='' additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': '{"expression": "', 'name': None}, 'type': None}]} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df' tool_calls=[{'name': '', 'args': {'expression': ''}, 'id': None, 'type': 'tool_call'}] tool_call_chunks=[{'name': None, 'args': '{"expression": "', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='' additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': '3', 'name': None}, 'type': None}]} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df' invalid_tool_calls=[{'name': None, 'args': '3', 'id': None, 'error': None, 'type': 'invalid_tool_call'}] tool_call_chunks=[{'name': None, 'args': '3', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='' additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': ' +', 'name': None}, 'type': None}]} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df' invalid_tool_calls=[{'name': None, 'args': ' +', 'id': None, 'error': None, 'type': 'invalid_tool_call'}] tool_call_chunks=[{'name': None, 'args': ' +', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='' additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': ' ', 'name': None}, 'type': None}]} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df' invalid_tool_calls=[{'name': None, 'args': ' ', 'id': None, 'error': None, 'type': 'invalid_tool_call'}] tool_call_chunks=[{'name': None, 'args': ' ', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='' additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': '2', 'name': None}, 'type': None}]} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df' invalid_tool_calls=[{'name': None, 'args': '2', 'id': None, 'error': None, 'type': 'invalid_tool_call'}] tool_call_chunks=[{'name': None, 'args': '2', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='' additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': ' *', 'name': None}, 'type': None}]} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df' invalid_tool_calls=[{'name': None, 'args': ' *', 'id': None, 'error': None, 'type': 'invalid_tool_call'}] tool_call_chunks=[{'name': None, 'args': ' *', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='' additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': ' ', 'name': None}, 'type': None}]} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df' invalid_tool_calls=[{'name': None, 'args': ' ', 'id': None, 'error': None, 'type': 'invalid_tool_call'}] tool_call_chunks=[{'name': None, 'args': ' ', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='' additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': '5', 'name': None}, 'type': None}]} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df' invalid_tool_calls=[{'name': None, 'args': '5', 'id': None, 'error': None, 'type': 'invalid_tool_call'}] tool_call_chunks=[{'name': None, 'args': '5', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='' additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': '"}', 'name': None}, 'type': None}]} response_metadata={} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df' invalid_tool_calls=[{'name': None, 'args': '"}', 'id': None, 'error': None, 'type': 'invalid_tool_call'}] tool_call_chunks=[{'name': None, 'args': '"}', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='' additional_kwargs={} response_metadata={'finish_reason': 'tool_calls', 'model_name': 'Qwen3-32B-AWQ'} id='run--eb447231-2a64-4c5a-926c-b8b60f5332df'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'checkpoint_ns': 'agent:f0041eb1-815a-56f0-fd55-f17bc4280a94', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='计算结果: 13' name='calculator' id='09c51072-2c94-4ba9-87f9-c18de130c790' tool_call_id='chatcmpl-tool-66acdf49f3cd47c68b9c408e79c5d1e2'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 2, 'langgraph_node': 'tools', 'langgraph_triggers': ('branch:to:tools',), 'langgraph_path': ('__pregel_pull', 'tools'), 'langgraph_checkpoint_ns': 'tools:4bfbf6f0-f896-8b34-23cc-43e9865642a8'}
----------------------------------------
Token: content='' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='<think>' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='\n' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='好的' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='用户' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='让我' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='计算' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='3' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='加' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='2' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='乘' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='以' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='5' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='。' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='首先' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='我' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='需要' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='确认' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='运算' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='顺序' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='。' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='根据' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='数学' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='规则' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='乘' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='法' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='优先' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='于' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='加' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='法' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='所以' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='先' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='算' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='2' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='乘' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='5' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='等于' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='1' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='0' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='再加上' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='3' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='结果' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='是' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='1' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='3' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='。' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='然后' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='我' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='调' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='用' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='计算器' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='工具' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='来' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='验证' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='这个' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='结果' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='是否' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='正确' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='。' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='工具' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='返回' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='的结果' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='确实是' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='1' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='3' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='所以' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='最终' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='答案' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='应该是' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='1' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='3' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='。' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='用户' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='可能' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='是在' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='做' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='数学' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='作业' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='或者' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='需要' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='快速' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='计算' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='确保' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='结果' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='正确' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='很重要' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='。' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='没有' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='其他' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='工具' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='需要' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='调' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='用' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='，' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='直接' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='给出' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='答案' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='即可' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='。\n' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='</think>' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='\n\n' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='计算' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='结果' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='为' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='：' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='1' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='3' additional_kwargs={} response_metadata={} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------
Token: content='' additional_kwargs={} response_metadata={'finish_reason': 'stop', 'model_name': 'Qwen3-32B-AWQ'} id='run--d79d3a11-4c6f-4baf-aca8-a2b2c971122c'
Metadata: {'thread_id': 'test_thread_messages', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'checkpoint_ns': 'agent:854fa325-d4da-9058-bc0b-5fcf6f8447a6', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096}
----------------------------------------

============================================================
🧪 测试 stream_mode='custom' (自定义数据流)
============================================================
📝 用户消息: 计算 3 + 2 * 5

🔄 流式输出 (custom模式):
Custom: {'type': 'tool_progress', 'message': '开始计算: 3 + 2 * 5'}
----------------------------------------
Custom: {'type': 'tool_progress', 'message': '计算完成，结果: 13'}
----------------------------------------

============================================================
🧪 测试多模式组合 ['messages', 'updates', 'custom']
============================================================
📝 用户消息: 计算 3 + 2 * 5

🔄 流式输出 (多模式):
Mode: messages
Chunk: (AIMessageChunk(content='', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='<think>', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='\n', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='好的', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='我现在', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='需要', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='解决', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='用户', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='的问题', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='：', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='计算', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='3', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' +', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' ', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='2', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' *', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' ', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='5', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='。', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='首先', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='我', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='应该', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='确定', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='用户', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='需要', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='的是', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='数学', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='运算', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='的结果', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='。', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='这个', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='表达', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='式', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='涉及到', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='加', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='法', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='和', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='乘', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='法', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='根据', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='数学', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='运算', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='的', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='优先', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='级', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='乘', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='法', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='应该', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='先', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='于', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='加', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='法', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='进行', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='。', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='所以', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='首先', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='计算', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='2', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='乘', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='以', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='5', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='得到', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='1', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='0', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='然后', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='再加上', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='3', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='结果', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='就是', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='1', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='3', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='。', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='用户', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='可能', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='希望', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='得到', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='正确的', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='计算', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='步骤', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='和', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='结果', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='或者', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='他们', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='可能', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='对', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='运算', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='顺序', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='有', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='疑问', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='。', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='我', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='需要', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='确认', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='是否', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='需要', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='使用', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='计算器', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='工具', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='来', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='执行', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='这个', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='计算', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='或者', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='是否有', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='其他', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='潜在', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='的需求', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='。', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='例如', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='用户', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='可能', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='想', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='确认', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='运算', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='顺序', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='是否', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='正确', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='或者', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='是否有', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='括', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='号', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='需要', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='考虑', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='。', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='不过', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='在这个', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='表达', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='式', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='中', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='没有', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='括', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='号', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='改变', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='优先', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='级', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='所以', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='直接', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='按照', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='先', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='乘', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='后', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='加', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='处理', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='。', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='接下来', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='我', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='应该', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='调', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='用', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='计算器', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='函数', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='来', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='验证', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='这个', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='计算', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='过程', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='是否', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='正确', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='确保', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='结果', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='准确', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='无', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='误', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='。', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='最后', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='将', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='步骤', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='清晰', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='地', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='解释', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='给', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='用户', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='确保', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='他们', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='理解', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='如何', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='得到', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='结果', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='。\n', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='</think>', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='\n\n', additional_kwargs={}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='', additional_kwargs={'tool_calls': [{'index': 0, 'id': 'chatcmpl-tool-a09009b7415741e18c515a6e3d9dad71', 'function': {'arguments': None, 'name': 'calculator'}, 'type': 'function'}]}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f', tool_calls=[{'name': 'calculator', 'args': {}, 'id': 'chatcmpl-tool-a09009b7415741e18c515a6e3d9dad71', 'type': 'tool_call'}], tool_call_chunks=[{'name': 'calculator', 'args': None, 'id': 'chatcmpl-tool-a09009b7415741e18c515a6e3d9dad71', 'index': 0, 'type': 'tool_call_chunk'}]), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='', additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': '{"expression": "', 'name': None}, 'type': None}]}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f', tool_calls=[{'name': '', 'args': {'expression': ''}, 'id': None, 'type': 'tool_call'}], tool_call_chunks=[{'name': None, 'args': '{"expression": "', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='', additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': '3', 'name': None}, 'type': None}]}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f', invalid_tool_calls=[{'name': None, 'args': '3', 'id': None, 'error': None, 'type': 'invalid_tool_call'}], tool_call_chunks=[{'name': None, 'args': '3', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='', additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': ' +', 'name': None}, 'type': None}]}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f', invalid_tool_calls=[{'name': None, 'args': ' +', 'id': None, 'error': None, 'type': 'invalid_tool_call'}], tool_call_chunks=[{'name': None, 'args': ' +', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='', additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': ' ', 'name': None}, 'type': None}]}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f', invalid_tool_calls=[{'name': None, 'args': ' ', 'id': None, 'error': None, 'type': 'invalid_tool_call'}], tool_call_chunks=[{'name': None, 'args': ' ', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='', additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': '2', 'name': None}, 'type': None}]}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f', invalid_tool_calls=[{'name': None, 'args': '2', 'id': None, 'error': None, 'type': 'invalid_tool_call'}], tool_call_chunks=[{'name': None, 'args': '2', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='', additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': ' *', 'name': None}, 'type': None}]}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f', invalid_tool_calls=[{'name': None, 'args': ' *', 'id': None, 'error': None, 'type': 'invalid_tool_call'}], tool_call_chunks=[{'name': None, 'args': ' *', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='', additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': ' ', 'name': None}, 'type': None}]}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f', invalid_tool_calls=[{'name': None, 'args': ' ', 'id': None, 'error': None, 'type': 'invalid_tool_call'}], tool_call_chunks=[{'name': None, 'args': ' ', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='', additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': '5', 'name': None}, 'type': None}]}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f', invalid_tool_calls=[{'name': None, 'args': '5', 'id': None, 'error': None, 'type': 'invalid_tool_call'}], tool_call_chunks=[{'name': None, 'args': '5', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='', additional_kwargs={'tool_calls': [{'index': 0, 'id': None, 'function': {'arguments': '"}', 'name': None}, 'type': None}]}, response_metadata={}, id='run--28941eca-24cc-494a-a475-68cd0518339f', invalid_tool_calls=[{'name': None, 'args': '"}', 'id': None, 'error': None, 'type': 'invalid_tool_call'}], tool_call_chunks=[{'name': None, 'args': '"}', 'id': None, 'index': 0, 'type': 'tool_call_chunk'}]), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='', additional_kwargs={}, response_metadata={'finish_reason': 'tool_calls', 'model_name': 'Qwen3-32B-AWQ'}, id='run--28941eca-24cc-494a-a475-68cd0518339f'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 1, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'checkpoint_ns': 'agent:9330f3a0-ade2-160c-86da-cf29d8160d8c', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: updates
Chunk: {'agent': {'messages': [AIMessage(content='<think>\n好的，我现在需要解决用户的问题：计算3 + 2 * 5。首先，我应该确定用户需要的是数学运算的结果。这个表达式涉及到加法和乘法，根据数学运算的优先级，乘法应该先于加法进行。所以，首先计算2乘以5，得到10，然后再加上3，结果就是13。用户可能希望得到正确的计算步骤和结果，或者他们可能对运算顺序有疑问。我需要确认是否需要使用计算器工具来执行这个计算，或者是否有其他潜在的需求。例如，用户可能想确认运算顺序是否正确，或者是否有括号需要考虑。不过在这个表达式中，没有括号改变优先级，所以直接按照先乘后加处理。接下来，我应该调用计算器函数来验证这个计算过程是否正确，确保结果准确无误。最后，将步骤清晰地解释给用户，确保他们理解如何得到结果。\n</think>\n\n', additional_kwargs={'tool_calls': [{'index': 0, 'id': 'chatcmpl-tool-a09009b7415741e18c515a6e3d9dad71', 'function': {'arguments': '{"expression": "3 + 2 * 5"}', 'name': 'calculator'}, 'type': 'function'}]}, response_metadata={'finish_reason': 'tool_calls', 'model_name': 'Qwen3-32B-AWQ'}, id='run--28941eca-24cc-494a-a475-68cd0518339f', tool_calls=[{'name': 'calculator', 'args': {'expression': '3 + 2 * 5'}, 'id': 'chatcmpl-tool-a09009b7415741e18c515a6e3d9dad71', 'type': 'tool_call'}])]}}
----------------------------------------
Mode: custom
Chunk: {'type': 'tool_progress', 'message': '开始计算: 3 + 2 * 5'}
----------------------------------------
Mode: custom
Chunk: {'type': 'tool_progress', 'message': '计算完成，结果: 13'}
----------------------------------------
Mode: messages
Chunk: (ToolMessage(content='计算结果: 13', name='calculator', id='564c38bf-5f16-4188-a32b-f34ca56a54dd', tool_call_id='chatcmpl-tool-a09009b7415741e18c515a6e3d9dad71'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 2, 'langgraph_node': 'tools', 'langgraph_triggers': ('branch:to:tools',), 'langgraph_path': ('__pregel_pull', 'tools'), 'langgraph_checkpoint_ns': 'tools:494ddf89-619a-91d0-184f-4c0d53f47ef4'})
----------------------------------------
Mode: updates
Chunk: {'tools': {'messages': [ToolMessage(content='计算结果: 13', name='calculator', id='564c38bf-5f16-4188-a32b-f34ca56a54dd', tool_call_id='chatcmpl-tool-a09009b7415741e18c515a6e3d9dad71')]}}
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='<think>', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='\n', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='好的', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='用户', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='让我', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='计算', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='3', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='加', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='2', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='乘', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='以', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='5', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='。', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='首先', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='我', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='需要', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='确定', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='运算', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='的', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='优先', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='级', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='。', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='根据', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='数学', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='规则', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='乘', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='法', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='应该', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='先', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='于', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='加', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='法', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='进行', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='。', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='所以', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='我', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='先', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='算', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='2', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='乘', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='以', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='5', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='得到', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='1', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='0', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='。', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='然后', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='再加上', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='3', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='结果', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='就是', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='1', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='3', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='。', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='为了', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='确保', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='正确', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='我', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='使用', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='了', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='计算器', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='工具', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='来', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='验证', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='这个', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='过程', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='。', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='计算', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='结果', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='确实是', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='1', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='3', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='所以', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='用户', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='的问题', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='得到了', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='正确的', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='解答', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='。', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='最后', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='我', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='需要', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='把', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='步骤', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='清楚', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='地', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='解释', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='给', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='用户', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='让他们', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='明白', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='为什么', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='结果', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='是', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='1', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='3', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='而', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='不会', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='误解', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='成', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='先', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='加', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='后', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='乘', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='的情况', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='。', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='这样', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='用户', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='就能', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='理解', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='运算', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='顺序', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='的重要性', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='了', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='。\n', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='</think>', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='\n\n', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='3', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' +', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' ', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='2', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' *', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' ', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='5', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' 的', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='计算', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='结果', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='是', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' **', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='1', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='3', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='**', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='。\n\n', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='计算', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='过程', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='遵循', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='数学', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='运算', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='优先', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='级', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='：\n', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='1', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='.', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' 先', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='进行', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='乘', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='法', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='运算', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='：', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='2', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' ×', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' ', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='5', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' =', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' ', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='1', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='0', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='\n', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='2', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='.', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' 再', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='进行', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='加', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='法', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='运算', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='：', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='3', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' +', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' ', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='1', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='0', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' =', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content=' ', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='1', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='3', additional_kwargs={}, response_metadata={}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: messages
Chunk: (AIMessageChunk(content='', additional_kwargs={}, response_metadata={'finish_reason': 'stop', 'model_name': 'Qwen3-32B-AWQ'}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d'), {'thread_id': 'test_thread_multiple', 'user_id': 1, 'langgraph_step': 3, 'langgraph_node': 'agent', 'langgraph_triggers': ('branch:to:agent',), 'langgraph_path': ('__pregel_pull', 'agent'), 'langgraph_checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'checkpoint_ns': 'agent:bc744c29-cb41-a1e1-c6ea-05b51fde0b35', 'ls_provider': 'openai', 'ls_model_name': 'Qwen3-32B-AWQ', 'ls_model_type': 'chat', 'ls_temperature': 0.7, 'ls_max_tokens': 4096})
----------------------------------------
Mode: updates
Chunk: {'agent': {'messages': [AIMessage(content='<think>\n好的，用户让我计算3加2乘以5。首先，我需要确定运算的优先级。根据数学规则，乘法应该先于加法进行。所以，我先算2乘以5，得到10。然后再加上3，结果就是13。为了确保正确，我使用了计算器工具来验证这个过程。计算结果确实是13，所以用户的问题得到了正确的解答。最后，我需要把步骤清楚地解释给用户，让他们明白为什么结果是13，而不会误解成先加后乘的情况。这样用户就能理解运算顺序的重要性了。\n</think>\n\n3 + 2 * 5 的计算结果是 **13**。\n\n计算过程遵循数学运算优先级：\n1. 先进行乘法运算：2 × 5 = 10\n2. 再进行加法运算：3 + 10 = 13', additional_kwargs={}, response_metadata={'finish_reason': 'stop', 'model_name': 'Qwen3-32B-AWQ'}, id='run--89e464bf-8ee4-49b4-ab38-1d79268c8e2d')]}}
----------------------------------------

✅ 所有测试完成
📄 完整输出已保存到: langgraph_streaming_test_20250910_171628.txt
