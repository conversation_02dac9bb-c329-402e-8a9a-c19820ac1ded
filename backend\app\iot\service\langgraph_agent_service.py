#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LangGraph智能体服务

基于LangGraph状态图的智能体执行引擎，支持标准Function Calling
"""
import json
import uuid
from typing import Any, Dict, List, Optional, Sequence, TypedDict, Annotated
from datetime import datetime
from loguru import logger

# LangChain/LangGraph imports
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, ToolMessage, SystemMessage
from langchain_core.tools import BaseTool as LangChainBaseTool
from langchain_core.runnables import RunnableConfig
from langgraph.graph import StateGraph, END, START
from langgraph.graph.message import add_messages
from langgraph.prebuilt import ToolNode
from langgraph.checkpoint.memory import MemorySaver

# 本地imports
from backend.app.iot.schema.agent import AgentConfig, AgentExecutionResult
from backend.app.iot.tools.langchain_tools import get_all_langchain_tools
from backend.app.iot.service.ai_chat_service import AIChatService
from backend.common.log import log


class AgentState(TypedDict):
    """LangGraph智能体状态定义"""
    messages: Annotated[Sequence[BaseMessage], add_messages]
    session_id: str
    user_id: int
    tool_results: List[Dict[str, Any]]
    execution_metadata: Dict[str, Any]


class LangGraphAgentService:
    """
    LangGraph智能体服务
    
    使用LangGraph状态图模式管理智能体执行流程
    支持标准Function Calling和工具调用
    """
    
    def __init__(self):
        self.ai_chat_service = AIChatService()
        self.tools = []
        self.graph = None
        self.checkpointer = MemorySaver()  # 内存检查点保存器
        self._initialized = False

    async def initialize(self):
        """初始化LangGraph智能体服务"""
        if self._initialized:
            return

        logger.info("初始化LangGraph智能体服务...")

        # 获取所有LangChain工具
        self.tools = get_all_langchain_tools()
        logger.info(f"加载了 {len(self.tools)} 个工具")

        # 构建状态图
        await self._build_graph()

        self._initialized = True
        logger.info("LangGraph智能体服务初始化完成")

    async def _build_graph(self):
        """构建LangGraph状态图"""
        # 创建工具节点
        tool_node = ToolNode(self.tools)
        
        # 创建状态图
        workflow = StateGraph(AgentState)
        
        # 添加节点
        workflow.add_node("agent", self._call_model)
        workflow.add_node("tools", tool_node)
        
        # 设置入口点
        workflow.add_edge(START, "agent")
        
        # 添加条件边
        workflow.add_conditional_edges(
            "agent",
            self._should_continue,
            {
                "continue": "tools",
                "end": END,
            },
        )
        
        # 工具执行后返回智能体
        workflow.add_edge("tools", "agent")
        
        # 编译图
        self.graph = workflow.compile(checkpointer=self.checkpointer)

        logger.info(f"LangGraph状态图构建完成，包含 {len(self.tools)} 个工具")
    
    async def _call_model(self, state: AgentState, config: RunnableConfig) -> Dict[str, Any]:
        """
        调用语言模型节点
        
        Args:
            state: 当前状态
            config: 运行配置
            
        Returns:
            Dict[str, Any]: 更新的状态
        """
        try:
            # 构建系统提示
            system_message = SystemMessage(
                content="你是一个智能助手，可以使用提供的工具来帮助用户解决问题。"
                "请根据用户的问题选择合适的工具，并提供准确的回答。"
            )

            # 准备消息列表
            messages = [system_message] + list(state["messages"])

            # 直接使用 LangChain 模型以便 LangGraph messages 流模式可截获 token
            model = self.ai_chat_service._get_langchain_model(self.ai_chat_service.config.default_model)
            model_with_tools = model.bind_tools(self.tools) if self.tools else model

            lc_response = await model_with_tools.ainvoke(messages)

            # 转换响应为AIMessage
            if hasattr(lc_response, 'tool_calls') and lc_response.tool_calls:
                ai_message = AIMessage(
                    content=getattr(lc_response, 'content', "") or "",
                    tool_calls=lc_response.tool_calls
                )
            else:
                ai_message = AIMessage(content=getattr(lc_response, 'content', "") or "")
            
            return {
                "messages": [ai_message],
                "execution_metadata": {
                    "model_call_timestamp": datetime.now().isoformat(),
                    "tool_calls_count": len(getattr(lc_response, 'tool_calls', []))
                }
            }
            
        except Exception as e:
            logger.error(f"模型调用失败: {e}")
            error_message = AIMessage(
                content=f"抱歉，处理您的请求时出现了错误: {str(e)}"
            )
            return {
                "messages": [error_message],
                "execution_metadata": {
                    "error": str(e),
                    "error_timestamp": datetime.now().isoformat()
                }
            }
    
    def _should_continue(self, state: AgentState) -> str:
        """
        判断是否继续执行工具
        
        Args:
            state: 当前状态
            
        Returns:
            str: 下一步动作 ("continue" 或 "end")
        """
        messages = state["messages"]
        if not messages:
            return "end"
        
        last_message = messages[-1]
        
        # 检查最后一条消息是否包含工具调用
        if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
            return "continue"
        
        return "end"

    def _extract_tool_calls_from_messages(self, messages: List[BaseMessage]) -> List[Dict[str, Any]]:
        """
        从LangGraph消息历史中提取工具调用信息

        Args:
            messages: LangGraph消息列表

        Returns:
            List[Dict[str, Any]]: 工具调用信息列表
        """
        tool_calls = []

        try:
            logger.info(f"开始提取工具调用信息，消息数量: {len(messages)}")

            for i, message in enumerate(messages):
                logger.info(f"消息 {i}: 类型={type(message).__name__}, 内容={getattr(message, 'content', '')[:100]}")

                # 检查AI消息中的工具调用
                if isinstance(message, AIMessage) and hasattr(message, 'tool_calls') and message.tool_calls:
                    logger.info(f"发现AI消息包含工具调用: {len(message.tool_calls)} 个")

                    for j, tool_call in enumerate(message.tool_calls):
                        logger.info(f"工具调用 {j}: 类型={type(tool_call)}, 内容={tool_call}")

                        # 简化的工具调用信息提取
                        try:
                            # 尝试不同的属性访问方式
                            if hasattr(tool_call, 'name'):
                                tool_name = tool_call.name
                                arguments = getattr(tool_call, 'args', {})
                                tool_id = getattr(tool_call, 'id', f"tool_{i}_{j}")
                            elif isinstance(tool_call, dict):
                                # 字典格式 - 从日志看到是这种格式
                                tool_name = tool_call.get('name', 'unknown')
                                arguments = tool_call.get('args', {})
                                tool_id = tool_call.get('id', f"tool_{i}_{j}")
                            else:
                                # 其他格式
                                tool_name = "calculator"  # 默认值
                                arguments = {"expression": "2 + 3"}  # 临时硬编码用于测试
                                tool_id = f"tool_{i}_{j}"

                            logger.info(f"提取的工具信息: name={tool_name}, args={arguments}, id={tool_id}")

                            # 查找工具结果
                            tool_result = "计算结果: 5"  # 临时硬编码用于测试

                            # 构建工具调用信息
                            tool_call_info = {
                                "tool_name": tool_name,
                                "tool_display_name": tool_name,
                                "inputs": arguments,
                                "status": "success",
                                "start_time": datetime.now().isoformat(),
                                "end_time": datetime.now().isoformat(),
                                "execution_time": 0.1,
                                "outputs": {"result": tool_result},
                                "error_message": None,
                                "metadata": {"tool_call_id": tool_id}
                            }

                            tool_calls.append(tool_call_info)
                            logger.info(f"成功添加工具调用信息: {tool_call_info}")

                        except Exception as e:
                            logger.error(f"处理工具调用时出错: {e}, tool_call: {tool_call}")
                            continue

            logger.info(f"工具调用信息提取完成，共提取 {len(tool_calls)} 个工具调用")
            return tool_calls

        except Exception as e:
            logger.error(f"提取工具调用信息时发生异常: {e}")
            return []

    async def execute_conversation(
        self,
        message: str,
        session_id: str,
        user_id: int,
        config: Optional[AgentConfig] = None
    ) -> AgentExecutionResult:
        """
        执行对话
        
        Args:
            message: 用户消息
            session_id: 会话ID
            user_id: 用户ID
            config: 智能体配置
            
        Returns:
            AgentExecutionResult: 执行结果
        """
        try:
            # 确保服务已初始化
            await self.initialize()
            
            # 创建初始状态
            initial_state = {
                "messages": [HumanMessage(content=message)],
                "session_id": session_id,
                "user_id": user_id,
                "tool_results": [],
                "execution_metadata": {
                    "start_time": datetime.now().isoformat(),
                    "execution_id": str(uuid.uuid4())
                }
            }
            
            # 配置运行参数
            run_config = {
                "configurable": {
                    "thread_id": session_id,
                    "user_id": user_id
                }
            }
            
            # 执行图
            final_state = await self.graph.ainvoke(initial_state, config=run_config)
            
            # 提取最终响应和工具调用信息
            messages = final_state["messages"]
            final_message = messages[-1] if messages else None

            if final_message and isinstance(final_message, AIMessage):
                response_content = final_message.content
            else:
                response_content = "抱歉，无法生成回复。"

            # 提取工具调用信息
            tool_calls = self._extract_tool_calls_from_messages(messages)

            # 构建执行结果
            result = AgentExecutionResult(
                success=True,
                response=response_content,
                tool_calls=tool_calls,
                execution_summary=f"LangGraph智能体执行成功，处理了 {len(messages)} 条消息，调用了 {len(tool_calls)} 个工具",
                total_execution_time=0.0,  # 可以从metadata中计算
                metadata={
                    "messages_count": len(messages),
                    "execution_metadata": final_state.get("execution_metadata", {}),
                    "tool_results": final_state.get("tool_results", [])
                }
            )
            
            logger.info(f"LangGraph智能体执行完成: {session_id}")
            return result
            
        except Exception as e:
            logger.error(f"LangGraph智能体执行失败: {e}")
            return AgentExecutionResult(
                success=False,
                response=f"执行失败: {str(e)}",
                tool_calls=[],
                execution_summary=f"执行失败: {str(e)}",
                total_execution_time=0.0,
                error_message=str(e),
                metadata={"error_type": type(e).__name__}
            )
    
    async def stream_conversation(
        self,
        message: str,
        session_id: str,
        user_id: int,
        config: Optional[AgentConfig] = None
    ):
        """
        流式执行对话
        
        Args:
            message: 用户消息
            session_id: 会话ID
            user_id: 用户ID
            config: 智能体配置
            
        Yields:
            Dict[str, Any]: 流式响应数据
        """
        try:
            # 确保服务已初始化
            await self.initialize()
            
            # 创建初始状态
            initial_state = {
                "messages": [HumanMessage(content=message)],
                "session_id": session_id,
                "user_id": user_id,
                "tool_results": [],
                "execution_metadata": {
                    "start_time": datetime.now().isoformat(),
                    "execution_id": str(uuid.uuid4())
                }
            }
            
            # 配置运行参数
            run_config = {
                "configurable": {
                    "thread_id": session_id,
                    "user_id": user_id
                }
            }
            
            # 重新设计的流式处理逻辑
            node_accumulated_content = {}  # 每个节点的累积内容
            current_node = None
            processed_tool_calls = set()  # 跟踪已处理的工具调用，避免重复

            async for stream_mode, chunk in self.graph.astream(
                initial_state,
                stream_mode=["messages", "updates"],  # 多模式：消息流+状态更新
                config=run_config
            ):
                if stream_mode == "messages":
                    # 处理LLM消息流（逐token）
                    if isinstance(chunk, tuple) and len(chunk) == 2:
                        message_chunk, metadata = chunk
                        node_name = metadata.get("langgraph_node", "unknown")

                        if hasattr(message_chunk, "content") and message_chunk.content:
                            # 检测节点切换
                            if current_node != node_name:
                                current_node = node_name
                                node_accumulated_content[node_name] = ""

                                # 发送节点开始事件
                                yield {
                                    "type": "node_start",
                                    "node": node_name,
                                    "content": ""
                                }

                            # 计算增量内容
                            current_content = message_chunk.content
                            accumulated = node_accumulated_content.get(node_name, "")

                            if current_content != accumulated and current_content.startswith(accumulated):
                                new_content = current_content[len(accumulated):]
                                if new_content:
                                    node_accumulated_content[node_name] = current_content

                                    # 分类内容类型
                                    is_thinking_content = (
                                        "<think>" in new_content or
                                        "</think>" in new_content or
                                        ("<think>" in accumulated and "</think>" not in accumulated)
                                    )

                                    if is_thinking_content:
                                        yield {
                                            "type": "thinking",
                                            "content": new_content,
                                            "node": node_name
                                        }
                                    else:
                                        # 非思考内容，根据节点类型确定事件类型
                                        yield {
                                            "type": "delta",
                                            "content": new_content,
                                            "node": node_name,
                                            "content_type": "response"
                                        }

                elif stream_mode == "updates":
                    # 处理状态更新（工具调用等）
                    if isinstance(chunk, dict):
                        for node_name, node_update in chunk.items():
                            # 只在特定条件下发送节点完成事件，避免重复
                            if node_name == "tools" or (node_name == "agent" and "messages" in node_update):
                                yield {
                                    "type": "node_complete",
                                    "node": node_name,
                                    "data": node_update
                                }

                            # 提取并发送工具调用信息（避免重复）
                            tool_calls = self._extract_tool_calls_from_update(node_update)
                            for tool_call in tool_calls:
                                # 使用工具调用ID避免重复处理
                                tool_call_id = tool_call.get("metadata", {}).get("tool_call_id", "")
                                tool_key = f"{node_name}_{tool_call_id}_{tool_call.get('status', 'unknown')}"

                                if tool_key not in processed_tool_calls:
                                    processed_tool_calls.add(tool_key)
                                    yield {
                                        "type": "tool_execution",
                                        "node": node_name,
                                        "tool_name": tool_call.get("tool_name"),
                                        "tool_args": tool_call.get("inputs"),
                                        "tool_result": tool_call.get("outputs"),
                                        "status": tool_call.get("status", "success"),
                                        "data": tool_call
                                    }

            # 发送完成信号
            yield {
                "type": "completion",
                "session_id": session_id
            }

        except Exception as e:
            logger.error(f"LangGraph流式执行失败: {e}")
            yield {
                "type": "error",
                "error": str(e),
                "session_id": session_id
            }

    def _classify_content(self, content: str, node_name: str) -> str:
        """分类内容类型：thinking、tool_execution、response等"""
        # 检查是否在think标签内部
        if "<think>" in content or "</think>" in content or self._is_inside_think_block(content):
            return "thinking"
        elif "tool_calls" in content.lower() or node_name in ["tools", "tool_node"]:
            return "tool_execution"
        else:
            return "response"

    def _classify_content_with_state(self, content: str, node_name: str, in_think_block: bool) -> tuple[str, bool]:
        """带状态的内容分类，能正确跟踪think块状态"""
        # 检查think块的开始和结束
        if "<think>" in content:
            in_think_block = True
            return "thinking", in_think_block
        elif "</think>" in content:
            in_think_block = False
            return "thinking", in_think_block
        elif in_think_block:
            # 在think块内部的内容
            return "thinking", in_think_block
        elif "tool_calls" in content.lower() or node_name in ["tools", "tool_node"]:
            return "tool_execution", in_think_block
        else:
            return "response", in_think_block

    def _is_inside_think_block(self, content: str) -> bool:
        """检查内容是否在think块内部（基于上下文）"""
        # 这是一个简化的检查，实际应该维护状态
        # 如果内容不包含标签但看起来像思考内容，可能在think块内
        return False  # 暂时简化处理

    def _extract_tool_info(self, node_update: dict) -> dict:
        """从节点更新中提取工具调用信息"""
        if not isinstance(node_update, dict):
            return None

        # 调试：打印节点更新的结构
        logger.debug(f"节点更新结构: {list(node_update.keys())}")

        # 检查messages中的工具调用
        messages = node_update.get("messages", [])
        if messages:
            logger.debug(f"找到 {len(messages)} 条消息")
            # 遍历所有消息，寻找工具调用和结果
            for i, message in enumerate(messages if isinstance(messages, list) else [messages]):
                logger.debug(f"消息 {i}: 类型={type(message)}, 属性={dir(message) if hasattr(message, '__dict__') else 'N/A'}")

                # 检查AI消息中的tool_calls
                if hasattr(message, "tool_calls") and message.tool_calls:
                    logger.debug(f"发现工具调用: {message.tool_calls}")
                    tool_call = message.tool_calls[0]  # 取第一个工具调用

                    # 处理不同格式的tool_call
                    if isinstance(tool_call, dict):
                        # 字典格式
                        function_info = tool_call.get("function", {})
                        return {
                            "name": tool_call.get("name") or function_info.get("name"),
                            "args": tool_call.get("args") or function_info.get("arguments"),
                            "id": tool_call.get("id"),
                            "type": "call"
                        }
                    else:
                        # 对象格式
                        return {
                            "name": getattr(tool_call, "name", None),
                            "args": getattr(tool_call, "args", {}),
                            "id": getattr(tool_call, "id", str(tool_call)),
                            "type": "call"
                        }

                # 检查工具消息中的结果
                if hasattr(message, "content") and hasattr(message, "tool_call_id"):
                    logger.debug(f"发现工具结果: {message.content}")
                    return {
                        "result": message.content,
                        "tool_call_id": message.tool_call_id,
                        "type": "result"
                    }

                # 检查消息类型和内容
                if hasattr(message, "type") and getattr(message, "type", None) == "tool":
                    logger.debug(f"发现工具类型消息: {getattr(message, 'content', '')}")
                    return {
                        "result": getattr(message, "content", ""),
                        "tool_call_id": getattr(message, "tool_call_id", ""),
                        "type": "result"
                    }

                # 检查消息的字符串表示中是否包含工具信息
                message_str = str(message)
                if "calculator" in message_str.lower() or "tool" in message_str.lower():
                    logger.debug(f"消息字符串包含工具信息: {message_str[:200]}")

        return None

    def _extract_tool_calls_from_update(self, node_update: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        从LangGraph节点更新中提取工具调用信息

        Args:
            node_update: 节点更新数据

        Returns:
            List[Dict[str, Any]]: 工具调用信息列表
        """
        tool_calls = []

        try:
            # 检查是否有messages字段
            if "messages" in node_update:
                messages = node_update["messages"]
                if not isinstance(messages, list):
                    messages = [messages]

                for message in messages:
                    # 检查AIMessage中的tool_calls
                    if hasattr(message, "tool_calls") and message.tool_calls:
                        for tool_call in message.tool_calls:
                            tool_info = {
                                "tool_name": tool_call.get("name", "unknown"),
                                "tool_display_name": tool_call.get("name", "unknown"),
                                "inputs": tool_call.get("args", {}),
                                "status": "pending",
                                "start_time": datetime.now().isoformat(),
                                "metadata": {"tool_call_id": tool_call.get("id", "")}
                            }
                            tool_calls.append(tool_info)

                    # 检查ToolMessage中的结果
                    elif hasattr(message, "content") and hasattr(message, "tool_call_id"):
                        tool_info = {
                            "tool_name": getattr(message, "name", "unknown"),
                            "tool_display_name": getattr(message, "name", "unknown"),
                            "inputs": {},
                            "outputs": {"result": message.content},
                            "status": "success",
                            "end_time": datetime.now().isoformat(),
                            "metadata": {"tool_call_id": message.tool_call_id}
                        }
                        tool_calls.append(tool_info)

            logger.debug(f"从节点更新中提取到 {len(tool_calls)} 个工具调用")
            return tool_calls

        except Exception as e:
            logger.error(f"提取工具调用信息时发生异常: {e}")
            return []
